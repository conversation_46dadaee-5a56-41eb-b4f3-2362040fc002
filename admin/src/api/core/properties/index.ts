import API from "../base";

class PropertyAPI extends API {
  public getAll() {
    return {
      addProperty: (options = {}) =>
        this.post({ url: "/property/add", ...options }),
      getCategoriesWithRooms: (options = {}) =>
        this.get({ url: "/property/roomCategory", ...options }),
      addRoomCategory: (options = {}) =>
        this.post({ url: "/property/roomCategory/add", ...options }),
    };
  }
}

const propertyApi = new PropertyAPI().getAll();

export { propertyApi as PropertyAPI };
