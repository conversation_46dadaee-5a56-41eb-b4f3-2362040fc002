import { PropertyAPI } from ".";
import { Mutations, Queries } from "./types";
// import { AuthAPI } from ".";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useMutateRooms() {
  // const queryClient = useQueryClient();

  const { mutateAsync: upsertRoomCategory } = useMutation({
    mutationKey: [Queries.QUERY, Queries.GET_CATEGORIES],
    mutationFn: async (data) => {
      const response = await PropertyAPI.addRoomCategory({ data });
      return response.data;
    },
    onSuccess: (data) => {},
  });

  return {
    upsertRoomCategory,
  };
}

export function useMutateProperty() {
  const queryClient = useQueryClient();

  const { mutateAsync: upsertProperty } = useMutation({
    mutationKey: [Mutations.MUTATION, Mutations.ADD_PROPERTY],
    mutationFn: async (data) => {
      const response = await PropertyAPI.addProperty({ data });
      return response.data;
    },
    onSuccess: (data) => {
      console.log("UPSERT PROPERTY DATA!!!!", data);
      queryClient.setQueryData(
        [Queries.QUERY, Queries.GET_USER],
        (oldData: any) => {
          console.log({ oldData });
          return {
            ...oldData,
            properties: oldData.properties?.find((p) => p.id === data.id)
              ? oldData.properties.map((p) => (p.id === data.id ? data : p))
              : [...oldData.properties, data],
          };
        }
      );
    },
  });

  return {
    upsertProperty,
  };
}
