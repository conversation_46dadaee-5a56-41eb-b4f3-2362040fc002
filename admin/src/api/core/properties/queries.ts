import { useQuery } from "@tanstack/react-query";
import { PropertyAPI } from ".";
import { Queries } from "./types";

export const useCategories = () => {
  return useQuery({
    queryKey: [Queries.QUERY, Queries.GET_CATEGORIES],
    queryFn: async () => {
      const response = await PropertyAPI.getCategoriesWithRooms();
      return response.data;
    },
    // throwOnError: false,
    // staleTime: Infinity,
  });
};
