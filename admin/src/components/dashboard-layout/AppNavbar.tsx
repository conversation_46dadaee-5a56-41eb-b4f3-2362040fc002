import { styled } from "@mui/material/styles";
import AppBar from "@mui/material/AppBar";
import Stack from "@mui/material/Stack";
import MuiToolbar from "@mui/material/Toolbar";
import ColorModeButton from "src/theme/ColorModeButton";
import SelectProperty from "./SelectProperty";

const Toolbar = styled(MuiToolbar)({
  width: "100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "start",
  justifyContent: "center",
  gap: "12px",
  paddingLeft: "16px !important",
  paddingRight: "16px !important",
  flexShrink: 0,
});

export default function AppNavbar() {
  return (
    <AppBar
      id="app-bar"
      position="sticky"
      sx={{
        boxShadow: 0,
        bgcolor: "transparent !important",
        backgroundImage: "none",
        top: 0,
        zIndex: 1201,
      }}
    >
      <Toolbar variant="regular" id="toolbar">
        <Stack
          direction="row"
          sx={{
            alignItems: "center",
            flexGrow: 1,
            width: "100%",
            gap: 1,
          }}
        >
          <Stack
            direction="row"
            spacing={1}
            sx={{ justifyContent: "center", mr: "auto" }}
          >
            <SelectProperty />
          </Stack>
          <ColorModeButton />
        </Stack>
      </Toolbar>
    </AppBar>
  );
}
