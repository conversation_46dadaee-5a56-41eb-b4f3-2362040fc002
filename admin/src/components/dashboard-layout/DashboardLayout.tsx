import { alpha, <PERSON>, But<PERSON>, CssBaseline, Stack } from "@mui/material";
import { ReactNode, useMemo } from "react";
import AppTheme from "src/theme/AppTheme";
// import {
//   //   chartsCustomizations,
//   dataGridCustomizations,
//   datePickersCustomizations,
//   treeViewCustomizations,
// } from "src/theme/customizations";
import SideBar from "./SideBar";
import { useAuth } from "src/hooks/useAuth";
import AppNavbar from "./AppNavbar";
import { PropertyDialog } from "./PropertyDialog";
import { propertyDialogState } from "src/atoms/property-dialog";
import { useAtom, useSetAtom } from "jotai";

interface DashboardLayoutProps {
  children: ReactNode;
}

const xThemeComponents = {
  //   ...chartsCustomizations,
  // ...dataGridCustomizations,
  // ...datePickersCustomizations,
  // ...treeViewCustomizations,
};

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const { user, userLogged } = useAuth();
  const [propertyDialog, setPropertyDialog] = useAtom(propertyDialogState);

  console.log({ user });
  const userHasProperties = useMemo(() => {
    return user?.properties && user?.properties.length > 0;
  }, [user]);

  return (
    <AppTheme themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      {user && userLogged ? (
        <Box
          sx={{
            display: "flex",
            position: "relative",
            overflow: "hidden",
            height: "100vh",
            width: "100vw",
          }}
        >
          <SideBar />
          <Box
            component="main"
            sx={(theme) => ({
              display: "flex",
              flexDirection: "column",
              flexGrow: 1,
              backgroundColor: theme.vars
                ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
                : alpha(theme.palette.background.default, 1),
              overflow: "auto",
            })}
          >
            <AppNavbar />

            {userHasProperties ? (
              children
            ) : (
              <Stack alignItems="center" justifyContent="center" flex={1}>
                <Button
                  onClick={() =>
                    setPropertyDialog((prev) => ({ ...prev, isOpen: true }))
                  }
                >
                  Add Property
                </Button>
              </Stack>
            )}
            {propertyDialog.isOpen && <PropertyDialog />}
          </Box>
        </Box>
      ) : (
        <>{children}</>
      )}
    </AppTheme>
  );
};

export default DashboardLayout;
