import {
  <PERSON>,
  <PERSON>ton,
  Dialog,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogContentText,
  DialogTitle,
  Divider,
} from "@mui/material";
import { useAtom } from "jotai";
import { FormProvider, useForm } from "react-hook-form";
import {
  defaultPropertyFileds,
  propertyDialogState,
} from "src/atoms/property-dialog";
import { FormSelect } from "../form/select/FormSelect";
import { Input } from "../form";
import { useMutateProperty } from "src/api/core/properties/mutations";

export const PropertyDialog = () => {
  const [propertyDialog, setPropertyDialog] = useAtom(propertyDialogState);
  const { upsertProperty } = useMutateProperty();
  console.log({ propertyDialog });
  const { isOpen, property } = propertyDialog;
  const closeDialog = () =>
    setPropertyDialog({ isOpen: false, property: defaultPropertyFileds });

  const form = useForm({
    defaultValues: property,
    //  resolver: yupR<PERSON>olver(SCHEMA),
  });
  const {
    handleSubmit,
    formState: { errors },
    getValues,
  } = form;
  console.log("ALL FORM VALUS!!!", getValues());

  const onSubmit = (data) => {
    console.log({ data });
    upsertProperty(data);
  };

  return (
    <Dialog
      open={isOpen}
      onClose={closeDialog}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      scroll="paper"
      keepMounted={false}
    >
      <DialogTitle id="alert-dialog-title">{"Add your property"}</DialogTitle>
      <FormProvider {...form}>
        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          noValidate
          sx={{
            display: "flex",
            flexDirection: "column",
            width: "100%",
            gap: 2,
          }}
        >
          <DialogContent dividers>
            <FormSelect
              name="type"
              label="Property type"
              options={[{ key: "hotel", value: "Hotel" }]}
              variant="outlined"
            />
            <Input
              id="name"
              name="name"
              label="Property name"
              autoComplete="name"
              required
              fullWidth
              placeholder="Nice hotel"
            />
            <p>logo upload</p>
            <Divider />
            <Input
              id="checkIn"
              name="checkIn"
              label="Check-in time"
              autoComplete="checkIn"
              type="time"
              // required
              fullWidth
              placeholder="Nice hotel"
            />
            <Input
              id="checkOut"
              name="checkOut"
              label="Check-out time"
              autoComplete="checkOut"
              type="time"
              // required
              fullWidth
              placeholder="Nice hotel"
            />
            <Divider />
            {/* Address */}
            <Input
              id="street"
              name="street"
              label="Street"
              autoComplete="street"
              required
              fullWidth
              placeholder="123 Main St"
            />
            <Input
              id="city"
              name="city"
              label="City"
              autoComplete="city"
              required
              fullWidth
              placeholder="New York"
            />
            <Input
              id="state"
              name="state"
              label="State"
              autoComplete="state"
              required
              fullWidth
              placeholder="NY"
            />
            <Input
              id="zip"
              name="zip"
              label="Zip"
              autoComplete="zip"
              required
              fullWidth
              placeholder="10001"
            />
            <Input
              id="country"
              name="country"
              label="Country"
              autoComplete="country"
              required
              fullWidth
              placeholder="USA"
            />
            <Divider />
            {/* Contacts */}
            <Input
              id="email"
              name="email"
              label="Email"
              autoComplete="email"
              required
              fullWidth
              placeholder="<EMAIL>"
            />
            <Input
              id="phone"
              name="phone"
              label="Phone"
              autoComplete="phone"
              required
              fullWidth
              placeholder="************"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={closeDialog} autoFocus>
              Cancel
            </Button>
            <Button type="submit">Save</Button>
          </DialogActions>
        </Box>
      </FormProvider>
    </Dialog>
  );
};
