import MuiAvatar from "@mui/material/Avatar";
import MuiListItemAvatar from "@mui/material/ListItemAvatar";
import MenuItem from "@mui/material/MenuItem";
import ListItemText from "@mui/material/ListItemText";
import ListItemIcon from "@mui/material/ListItemIcon";
import Select, { SelectChangeEvent, selectClasses } from "@mui/material/Select";
import Divider from "@mui/material/Divider";
import { styled } from "@mui/material/styles";
import AddRoundedIcon from "@mui/icons-material/AddRounded";
import { useMemo, useState } from "react";
import { useSetAtom } from "jotai";
import {
  defaultPropertyFileds,
  propertyDialogState,
} from "src/atoms/property-dialog";
import { useAuth } from "src/hooks/useAuth";
import {
  HomeWork as HomeWorkIcon,
  EditSquare as EditSquareIcon,
} from "@mui/icons-material";
import { useParams } from "react-router-dom";

const Avatar = styled(MuiAvatar)(({ theme }) => ({
  width: 28,
  height: 28,
  backgroundColor: (theme.vars || theme).palette.background.paper,
  color: (theme.vars || theme).palette.text.secondary,
  border: `1px solid ${(theme.vars || theme).palette.divider}`,
}));

const ListItemAvatar = styled(MuiListItemAvatar)({
  minWidth: 0,
  marginRight: 12,
});

export default function SelectProperty() {
  const setPropertyDialog = useSetAtom(propertyDialogState);
  const { propertyId } = useParams();
  console.log({ propertyId });
  const { user } = useAuth();
  // TODO: rename to property select, do request query and the functional to add property here
  const [property, setProperty] = useState("");
  console.log({ property });
  const handleChange = (event: SelectChangeEvent) => {
    const { value } = event.target;
    if (value === "new") {
      setPropertyDialog({ isOpen: true, property: defaultPropertyFileds });
    }
    setProperty(event.target.value as string);
  };

  const options = useMemo(() => {
    return user?.properties.map((property) => ({
      key: property.id,
      value: property.name,
    }));
  }, [user]);
  console.log({ options });

  return (
    <Select
      // labelId="property-select"
      id="property-simple-select"
      value={propertyId || "new"}
      onChange={handleChange}
      displayEmpty
      inputProps={{ "aria-label": "Select property" }}
      fullWidth
      renderValue={(value) => {
        return (
          <>
            <ListItemAvatar>
              <Avatar alt="hotel-icon">
                <HomeWorkIcon sx={{ fontSize: "1rem" }} />
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={options?.find((o) => o.key === value)?.value || value}
            />
          </>
        );
      }}
      sx={{
        maxHeight: 40,
        width: 223,
        "&.MuiList-root": {
          p: "8px",
        },
        [`& .${selectClasses.select}`]: {
          display: "flex",
          alignItems: "center",
          gap: "2px",
          pl: 1,
        },
      }}
    >
      {options?.map((option) => {
        return (
          <MenuItem key={option.key} value={option.key}>
            <ListItemAvatar>
              <Avatar alt={option.value}>
                <HomeWorkIcon sx={{ fontSize: "1rem" }} />
              </Avatar>
            </ListItemAvatar>
            <ListItemText primary={option.value} />
            <ListItemAvatar
              onClick={(e) => {
                e.stopPropagation();
                setPropertyDialog({
                  isOpen: true,
                  property: user?.properties?.find((p) => p.id === option.key),
                });
              }}
            >
              <Avatar alt={option.value}>
                <EditSquareIcon sx={{ fontSize: "1rem" }} />
              </Avatar>
            </ListItemAvatar>
          </MenuItem>
        );
      })}

      {options?.length > 0 && <Divider sx={{ mx: -1 }} />}
      <MenuItem value="new">
        <ListItemIcon>
          <AddRoundedIcon />
        </ListItemIcon>
        <ListItemText primary="Add property" />
      </MenuItem>
    </Select>
  );
}
