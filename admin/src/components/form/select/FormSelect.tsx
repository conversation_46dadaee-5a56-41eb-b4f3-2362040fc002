import { forwardRef } from "react";
import { Controller, get, useFormState } from "react-hook-form";

import { TSelectProps } from "./types";
import { SimpleSelect } from "src/components/simple-select/SimpleSelect";

export const FormSelect = forwardRef(
  (
    {
      options,
      name,
      label,
      tooltip,
      onValueChange,
      showNone,
      ...rest
    }: TSelectProps,
    ref
  ) => {
    const { errors } = useFormState();
    const errorMessage = get(errors, `${name}.message`);

    return (
      <Controller
        name={name}
        render={({ field }) => (
          <SimpleSelect
            showNone={showNone}
            formFields={field}
            label={label}
            tooltip={tooltip}
            options={options}
            // input={<OutlinedInput label={label} />}
            name={name}
            error={!!errorMessage}
            helperText={errorMessage}
            onValueChange={onValueChange}
            {...rest}
            ref={ref}
          />
        )}
      />
    );
  }
);
