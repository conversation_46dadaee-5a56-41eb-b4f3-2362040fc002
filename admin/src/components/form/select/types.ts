import { SelectProps } from "@mui/material";
import { ReactElement } from "react";

export type TSelectOption = {
  key: string | number;
  value: string | ReactElement;
  label?: string | ReactElement;
  locked?: boolean;
  disabled?: boolean;
  tooltip?: string;
};

export type TAutocompleteOption = {
  inputValue?: string;
  key: string | number;
  value: string | ReactElement;
};

export type TSearchSelectOption = {
  label: string | number;
  value: string | ReactElement;
};

export type TSelectProps = SelectProps & {
  name: string;
  options: TSelectOption[];
  tooltip?: string;
  helperText?: string;
  showNone?: boolean;
  onValueChange?: (value: string) => void;
};
