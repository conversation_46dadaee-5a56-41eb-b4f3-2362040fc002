import { forwardRef } from "react";
import {
  Select as MuiSelect,
  FormControl,
  MenuItem,
  Stack,
  Tooltip,
  Typography,
  FormLabel,
} from "@mui/material";

import { TSimpleSelectProps } from "./types";

export const SimpleSelect = forwardRef(function Select<T>(
  {
    options,
    fullWidth = true,
    label,
    tooltip,
    name,
    formFields,
    helperText,
    error,
    onValueChange,
    showNone,
    ...rest
  }: TSimpleSelectProps<T>,
  ref: React.ForwardedRef<unknown>
) {
  if (
    formFields?.value &&
    showNone &&
    !options.find(
      (e) =>
        e.key === "null" ||
        (typeof e.value === "string" && e.value.toLowerCase() === "none")
    )
  ) {
    options.push({ key: "", value: "None" });
  }

  return (
    <FormControl fullWidth={fullWidth}>
      {label && (
        <FormLabel {...(name && { id: `${name}-label` })}>{label}</FormLabel>
      )}
      <Stack direction="row">
        <Stack direction="column" width="100%">
          <MuiSelect
            {...(name && { id: `${name}-label` })}
            label={label}
            fullWidth
            error={error}
            {...formFields}
            onChange={(e) => {
              formFields?.onChange(e);
              if (onValueChange) onValueChange(e.target.value as string);
            }}
            {...rest}
            ref={ref}
          >
            {options?.length ? (
              options?.map((option, index) => {
                const item = (
                  <MenuItem
                    key={`${option.key}_${index}`}
                    value={option.key}
                    disabled={option.locked || option.disabled}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography noWrap>{option.value}</Typography>
                    {/* {option.locked && <FAIcon icon={faLock} />} */}
                  </MenuItem>
                );
                return option.tooltip ? (
                  <Tooltip
                    key={`${option.key}_${index}`}
                    title={option.tooltip}
                    placement="top"
                  >
                    {item}
                  </Tooltip>
                ) : (
                  item
                );
              })
            ) : (
              <Typography sx={{ paddingX: 2, paddingY: 1 }}>
                no values
              </Typography>
            )}
          </MuiSelect>
          {helperText && (
            <Typography
              variant="caption"
              color={error ? "error" : "inherit"}
              paddingLeft={1}
              marginTop={0.5}
            >
              {helperText}
            </Typography>
          )}
        </Stack>
        {tooltip && (
          <Tooltip sx={{ ml: 1 }} title={tooltip} placement="top">
            <>i</>
            {/* <FAIcon fontSize={IconSize.Small} icon={faInfoCircle} /> */}
          </Tooltip>
        )}
      </Stack>
    </FormControl>
  );
});
