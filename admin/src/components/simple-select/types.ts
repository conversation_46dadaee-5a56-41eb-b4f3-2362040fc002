import { SelectProps } from '@mui/material';
import { FieldValues } from 'react-hook-form';

import { TSelectOption } from '../form/select/types';

export type TSimpleSelectProps<T> = SelectProps<T> & {
  options: TSelectOption[];
  name?: string;
  tooltip?: string;
  fullWidth?: boolean;
  formFields?: FieldValues;
  helperText?: string;
  showNone?: boolean;
  onValueChange?: (value: string) => void;
};
