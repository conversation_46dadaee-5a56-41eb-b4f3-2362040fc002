import { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import DashboardLayout from "src/components/dashboard-layout/DashboardLayout";
import { useAuth } from "src/hooks/useAuth";

export const AuthGuard = ({ children }: { children: ReactNode }) => {
  const { user, userLogged } = useAuth();

  if (!user && !userLogged) {
    return <Navigate to="/auth" />;
  }

  return <DashboardLayout>{children}</DashboardLayout>;
};
