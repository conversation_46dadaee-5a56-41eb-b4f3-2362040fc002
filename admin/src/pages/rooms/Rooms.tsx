import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  Button,
  IconButton,
  Typography,
  Box,
  Chip,
  Menu,
  MenuItem,
  Collapse,
} from "@mui/material";
import {
  Add as AddIcon,
  MoreHoriz as MoreHorizIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import { RoomCategory } from "./types";
import { CategoryRow } from "./components/category-row/CategoryRow";
import { CategoryDrawer } from "./components/category-drawer/CategoryDrawer";
import { useCategories } from "src/api/core/properties/queries";
// import { useCategories } from "src/tanstack/rooms";

export const Rooms = () => {
  const { data: fetchedCategories } = useCategories();
  // const [categories, setCategories] = useState<RoomCategory[]>(initialData);
  const [categoryDrawerOpen, setCategoryDrawerOpen] = useState(false);

  const handleToggle = (id: number) => {
    // setCategories((prev) =>
    //   prev.map((category) =>
    //     category.id === id
    //       ? { ...category, enabled: !category.enabled }
    //       : category
    //   )
    // );
  };

  const openCategoryDrawer = () => {
    setCategoryDrawerOpen(true);
  };

  const closeCategoryDrawer = () => {
    setCategoryDrawerOpen(false);
  };

  return (
    <Box
      sx={{
        p: 3,
        // backgroundColor: "#f5f5f5",
        // minHeight: "100vh",
      }}
    >
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ mb: 2, fontWeight: 400 }}>
          Room Categories
        </Typography>
        <Button
          onClick={openCategoryDrawer}
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            // backgroundColor: "#9e9e9e",
            // color: "white",
            textTransform: "none",
            "&:hover": {
              // backgroundColor: "#757575",
            },
          }}
        >
          Add Category
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: 40 }}></TableCell>
              <TableCell>Category Name</TableCell>
              <TableCell>Short Name</TableCell>
              <TableCell sx={{ textAlign: "center" }}>Photos</TableCell>
              <TableCell sx={{ textAlign: "center" }}>Main Places</TableCell>
              <TableCell sx={{ textAlign: "center" }}>
                Additional Places
              </TableCell>
              <TableCell sx={{ textAlign: "center" }}>Enabled</TableCell>
              <TableCell sx={{ textAlign: "center" }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {fetchedCategories?.length > 0 ? (
              fetchedCategories.map((category) => (
                <CategoryRow
                  key={category.id}
                  category={category}
                  onToggle={handleToggle}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8}>
                  <Typography
                    variant="h6"
                    component="div"
                    sx={{ textAlign: "center" }}
                  >
                    No categories found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <CategoryDrawer open={categoryDrawerOpen} onClose={closeCategoryDrawer} />
    </Box>
  );
};
