import { Box, Drawer } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import { CategoryInputs } from "./types";
import { Input } from "src/components/form";
import { EditDrawer } from "src/components/edit-drawer/EditDrawer";
import { DrawerWidth } from "src/components/edit-drawer/types";
import { useMutateRooms } from "src/api/core/properties/mutations";

interface CategoryDrawerProps {
  open: boolean;
  onClose: () => void;
}

export const CategoryDrawer = ({ open, onClose }: CategoryDrawerProps) => {
  const { upsertRoomCategory } = useMutateRooms();
  const form = useForm<CategoryInputs>({
    defaultValues: {
      name: "",
      shortName: "",
      mainPlaces: 2,
      additionalPlaces: 0,
    },
  });

  const {
    handleSubmit,
    formState: { isDirty },
  } = form;

  const onSubmit = async (data: CategoryInputs) => {
    await upsertRoomCategory({ ...data, propertyId: 2 });
  };

  return (
    <EditDrawer
      // anchor={"right"}
      width={DrawerWidth.LG}
      open={open}
      onClose={onClose}
      hasChanged={isDirty}
      formId="upsert-category"
    >
      {/* <Box width="40vw" height="100%" role="presentation"> */}

      <FormProvider {...form}>
        <form id="upsert-category" onSubmit={handleSubmit(onSubmit)}>
          <Input name="name" label={"Name"} autoComplete="username" />
          <Input name="shortName" label={"Short Name"} />
          <Input name="mainPlaces" label={"Main Places"} />
          <Input name="additionalPlaces" label={"Additional Places"} />
        </form>
      </FormProvider>
      {/* </Box> */}
    </EditDrawer>
  );
};
