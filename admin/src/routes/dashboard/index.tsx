import { lazy } from "react";
import { Loadable } from "src/components/loadable/Loadable";
import { AuthGuard } from "src/guards/auth-guard/AuthGuard";
import { DASHBOARD_PATHS } from "./paths";

const Dashboard = Loadable(lazy(() => import("src/pages/dashboard")));

export default {
  path: DASHBOARD_PATHS.root,
  element: (
    <AuthGuard>
      <Dashboard />
    </AuthGuard>
  ),
  children: [],
};
