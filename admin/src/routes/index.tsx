import { useRoutes } from "react-router-dom";
import { default as DashBoard } from "src/routes/dashboard";
import { default as Rooms } from "src/routes/rooms";
import { default as Auth } from "src/routes/auth";

const routes = [Auth, DashBoard, Rooms];
export default function AppRoutes() {
  console.log({ routes });
  const element = useRoutes(routes);
  console.log({ element });
  return useRoutes(routes); // ProfileRoutes is the exported object
}
