// Takes a custom key and replaces it with the param
function insertPathParam(path: string, param: string | number, key: string) {
  if (key && path.includes(key)) {
    return path.replace(key, `${param}`);
  }

  return path;
}

function insertPathParams(path: string, params: Record<string, string>) {
  let newPath = path;
  Object.entries(params).forEach(([key, value]) => {
    newPath = insertPathParam(newPath, value, key);
  });

  return newPath;
}

export const pathUtils = {
  insertPathParam,
  insertPathParams,
};
