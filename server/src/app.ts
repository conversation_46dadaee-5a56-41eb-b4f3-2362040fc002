import * as http from "node:http";
import * as path from "node:path";

import * as bodyParser from "body-parser";
import * as express from "express";
import * as session from "express-session";
import * as redis from "redis";
import { errorLogging } from "src/middlewares/requestLogging";
import * as swaggerUi from "swagger-ui-express";

import { ClientException, Code } from "./core/common";
import { createSocket } from "./lib/socket";
import { logger } from "./log";
import { correlationIdMiddleware } from "./middlewares/correlationId";
import uiApiAuth from "./middlewares/uiApiAuth";
import BillingRouter from "./routers/billing.router";
import PaypalRouter from "./routers/paypal.router";
import StripeRouter from "./routers/stripe.router";
import UserRouter from "./routers/user.router";
import * as helmet from "helmet";
const RedisStore = require("connect-redis")(session);
import * as cors from "cors";
import * as cookieParser from "cookie-parser";
import AuthRouter from "./routers/auth/auth.router";
import PropertyRouter from "./routers/properties/properties.router";
import authMiddleware from "./middlewares/authMiddleware";
// const swaggerDocument = require("../swagger.json");

declare global {
  namespace Express {
    interface Request {
      io: any;
      account: any;
      logger: any;
    }
  }
}

export class App {
  public express: express.Application;

  public io: any;

  public server: any;

  constructor(port: number) {
    this.express = express();
    this.express.set("port", port);
    this.server = http.createServer(this.express);
    this.io = createSocket(this.server);
    this.middleware();
    this.routes();
  }

  private middleware(): void {
    const redisClient = redis.createClient({ url: process.env.REDISCLOUD_URL });
    this.express.use(correlationIdMiddleware);
    this.express.use(
      helmet({
        frameguard: {
          action: "deny",
        },
        hsts: {
          maxAge: 31_536_000,
        },
      })
    );
    this.express.use(cookieParser());

    const corsOptions = {
      origin: (origin, callback) => {
        callback(null, true);
      },
      allowedHeaders: [
        "Origin",
        "X-Requested-With",
        "Content-Type",
        "Accept",
        "Authorization",
        "X-API-Key",
        "host",
        "x-access-token",
        "X-Device-Fingerprint",
      ],
      credentials: true,
    };
    this.express.use(cors(corsOptions));
    this.express.options("*", cors(corsOptions));

    this.express.use(
      session({
        store: new RedisStore({
          client: redisClient,
        }),
        save: false,
        saveUninitialized: false,
        secret: process.env.SESSION_SECRET || "gridevo",
        resave: false,
      })
    );

    this.express.use(bodyParser.json({ limit: "20mb" }));
    this.express.use(
      bodyParser.urlencoded({
        limit: "20mb",
        extended: true,
        parameterLimit: 100,
      })
    );

    this.express.set("trust proxy", true);

    // Add headers
    this.express.use(async (request, res, next) => {
      const origin = request.get("origin") || "";
      res.header("Access-Control-Allow-Origin", origin);
      res.header("Access-Control-Allow-Credentials", "true");
      res.header("Access-Control-Allow-Methods", "GET,OPTIONS,POST,PUT,DELETE");
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );
      res.removeHeader("server");
      request.io = await this.io;
      request.logger = logger;
      next();
    });

    this.express.use(
      "/v1/static",
      express.static(path.join(process.cwd(), "./static"))
    );
  }

  // Configure API endpoints.
  private async routes(): Promise<void> {
    const router = express.Router();

    router.get("/", (request, res) => {
      res.redirect("https://gridevo.com");
    });
    // Add this debug middleware
    this.express.use((req, res, next) => {
      console.log(`Incoming request: ${req.method} ${req.url}`);
      next();
    });
    // Auth router
    this.express.use("/v1/auth", new AuthRouter().router);
    this.express.use(
      "/v1/property",
      authMiddleware,
      new PropertyRouter().router
    );

    //Public routes
    // this.express.use("/v1/paypal", new PaypalRouter().router);
    // this.express.use("/v1/stripe", new StripeRouter().router);

    //Private listeners routes
    // this.express.use("/v1/notify/", this.authorizeNotifier.bind(this));

    this.express.use(
      "/api-docs",
      uiApiAuth,
      swaggerUi.serve
      // swaggerUi.setup(swaggerDocument, {
      //   explorer: true,
      // })
    );

    // this.express.use("/v1/admin/user", new UserRouter().router);
    // this.express.use("/v1/admin/billing", new BillingRouter().router);

    this.express.use((error, request, res, next) => {
      if (error.code === "LIMIT_FILE_SIZE") {
        // Error from Multer
        return res
          .status(Code.VALIDATION_ERROR.code)
          .json({ message: "File is too large" });
      } else if (error instanceof ClientException) {
        errorLogging(request, res, () => {});
        return res.status(error?.status).json({ message: error.message });
      } else if (error) {
        errorLogging(request, res, () => {});
        logger.error(
          `${error?.isDatabase ? "DatabaseErrorMessage" : "Message"} : ${
            error.message
          } `,
          {
            stack: error.stack,
          }
        );
        return res
          .status(error?.status || error?.code || Code.BAD_REQUEST_ERROR.code)
          .json({
            message: Code.INTERNAL_ERROR.message,
          });
      }
      return next();
    });
  }

  // private authorizeNotifier(
  //   request: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   if (request.method === "OPTIONS") {
  //     return next();
  //   }

  //   try {
  //     if (request.get("Authorization") !== process.env.NOTIFY_TOKEN) {
  //       throw ClientException.new({
  //         code: Code.UNAUTHORIZED_ERROR,
  //       });
  //     }
  //     return next();
  //   } catch {
  //     res.status(Code.UNAUTHORIZED_ERROR.code).end();
  //   }
  // }
}
