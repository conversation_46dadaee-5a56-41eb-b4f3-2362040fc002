import { Request, Response } from "express";
import { CoreApiResponse, handleError } from "src/core/common";
import { DbProperty } from "src/db/lib/property/property";
import { DbRoomCategory } from "src/db/lib/property/roomCategory";

class PropertyController {
  queues;

  constructor(queues) {
    this.queues = queues;
  }

  @handleError("property/addProperty")
  public async addProperty(req: Request, res: Response) {
    try {
      const data = req.body;
      console.log(req.account);
      const userId = req.account.userId;
      console.log({ userId });
      console.log("ADDD PROPETY DATA!!!", data);
      const property = new DbProperty();
      Object.assign(property, {
        userId,
        ...data,
      });
      const savedProperty = await property.save();
      return res.json(CoreApiResponse.success(savedProperty));
    } catch (error) {
      return null;
    }
  }

  @handleError("rooms/getCategoriesWithRooms")
  public async getCategoriesWithRooms(req, res) {
    try {
      const categories = await new DbRoomCategory().getRoomCategoriesWithRooms(
        2
      );
      return res.json(CoreApiResponse.success(categories));
    } catch (error) {
      return null;
    }
  }

  public async addRoomCategory(data) {
    try {
      const category = new DbRoomCategory();
      Object.assign(category, data);
      const savedCategory = await category.save();
      return savedCategory;
    } catch (error) {
      return null;
    }
  }
}

export default PropertyController;
