const db = require("../../models");
import { Model } from "../model";

export class Property {
  id?: number;

  name: string;

  type: string;

  checkIn: string;

  checkOut: string;

  street: string;

  city: string;

  state: string;

  zip: string;

  country: string;

  email: string;

  phone: string;

  constructor() {
    this.name = "";
  }
}

export class DbProperty extends Model<DbProperty, Property> {
  constructor() {
    super(db.property, new Property());
  }
}
