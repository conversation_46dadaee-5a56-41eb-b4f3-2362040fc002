import config from "src/config";
import { NextFunction, Request, Response } from "express";
import {
  Code,
  CoreApiResponse,
  getRefreshTokenCookieSettings,
  REFRESH_TOKEN,
  UserType,
} from "src/core/common";
import { createAuthTokens } from "src/helpers/utils";

import * as jose from "jose";
const { base64url, jwtVerify } = jose;

const initUserMiddleware = (
  request: Request,
  res: Response,
  next: NextFunction
) => {
  (async () => {
    const refreshToken = request.cookies[REFRESH_TOKEN];

    if (!refreshToken) {
      return res
        .status(Code.UNAUTHORIZED_ERROR.code)
        .json(
          CoreApiResponse.error(
            Code.UNAUTHORIZED_ERROR.code,
            Code.UNAUTHORIZED_ERROR.message
          )
        );
    }

    try {
      const { payload } = await jwtVerify(
        refreshToken,
        base64url.decode(config.get("jwt.refreshSecret"))
      );

      const decoded = {
        userId: payload.userId as number,
        userType: payload.userType as UserType,
      };

      // Issue new tokens (rotate refresh token)
      const { accessToken, refreshToken: newRefreshToken } =
        await createAuthTokens(decoded.userId, decoded.userType);

      res.setHeader("x-access-token", accessToken);
      res.cookie(
        REFRESH_TOKEN,
        newRefreshToken,
        getRefreshTokenCookieSettings()
      );
      request.account = decoded;
      next();
    } catch {
      return res
        .status(Code.UNAUTHORIZED_ERROR.code)
        .json(
          CoreApiResponse.error(
            Code.UNAUTHORIZED_ERROR.code,
            Code.UNAUTHORIZED_ERROR.message
          )
        );
    }
  })();
};

export default initUserMiddleware;
