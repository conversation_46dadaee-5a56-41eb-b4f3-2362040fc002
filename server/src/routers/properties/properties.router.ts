import { Request, Response, Router } from "express";

import { handleError, RequestType, UserType } from "src/core/common";

import PropertyController from "src/controllers/property/propertyController";
// import authorize from "src/lib/permission";

class PropertyRouter {
  router: Router;

  propertyController: PropertyController;

  constructor() {
    this.router = Router();
    this.propertyController = new PropertyController({});
    this.init();
  }

  @handleError("rooms/addRoomCategory")
  public async addRoomCategory(request: RequestType<Request>, res: Response) {
    const categories = await this.propertyController.addRoomCategory(
      request.body
    );
    return res.status(200).json({ message: "Success", categories });
  }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */
  init() {
    this.router.post("/add", this.propertyController.addProperty.bind(this));
    this.router.get(
      "/roomCategory",
      this.propertyController.getCategoriesWithRooms.bind(this)
    );
    this.router.post("/roomCategory/add", this.addRoomCategory.bind(this));
  }
}

export default PropertyRouter;
